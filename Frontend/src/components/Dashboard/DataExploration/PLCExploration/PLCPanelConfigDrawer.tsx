import React, { useState, useEffect } from 'react';
import {
  Drawer,
  Form,
  Button,
  DatePicker,
  Select,
  Checkbox,
  Typography,
  message,
  Spin
} from 'antd';
import { CloseOutlined, SettingOutlined } from '@ant-design/icons';
import {
  PLCPanelConfigDrawerProps,
  PLCPanelConfiguration
} from './types/PLCTypes';
import { postRequest } from '../../../../utils/apiHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const PLCPanelConfigDrawer: React.FC<PLCPanelConfigDrawerProps> = ({
  open,
  onClose,
  panelId,
  configuration,
  onConfigurationSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [windowMode, setWindowMode] = useState(false);
   const [selectedGroup, setSelectedGroup] = useState<string>('BatchId');
  const [selectedTargets, setSelectedTargets] = useState<string[]>([]);
  const [windowLoading, setWindowLoading] = useState(false);
  const [yAxisLoading, setYAxisLoading] = useState(false);
  const [xAxisLoading, setXAxisLoading] = useState(false);
  const [groupLoading, setGroupLoading] = useState(false);
  const [fullScreenLoading, setFullScreenLoading] = useState(false);

  // New state for batch management
  const [availableBatches, setAvailableBatches] = useState<string[]>([]);
  const [selectedBatches, setSelectedBatches] = useState<string[]>([]);
  const [compareLimit, setCompareLimit] = useState<number>(5);
  const [batchMetaLoading, setBatchMetaLoading] = useState(false);
  const [isDataLoaded, setIsDataLoaded] = useState(false);

  // Function to fetch batch metadata
  const fetchBatchMetadata = async (startDateTime: any, endDateTime: any) => {
    setBatchMetaLoading(true);
    try {
      const apiPayload = {
        start_datetime: startDateTime.format('YYYY-MM-DD HH:mm'),
        end_datetime: endDateTime.format('YYYY-MM-DD HH:mm')
      };

      const response = await postRequest('/file/explore-plc/get-batch-meta', apiPayload);

      if (response.data.data) {
        const { batch_ids, compare_limit } = response.data.data;
        setAvailableBatches(batch_ids || []);
        setCompareLimit(compare_limit || 5);
        setSelectedBatches([]); // Reset selected batches
        // Don't show success message - just proceed to batch selection
        return true; // Success
      } else {
        message.error('Failed to fetch batch metadata');
        setAvailableBatches([]);
        setCompareLimit(5);
        return false; // Failure
      }
    } catch (error) {
      console.error('Error fetching batch metadata:', error);
      message.error('Error fetching batch metadata');
      setAvailableBatches([]);
      setCompareLimit(5);
      return false; // Failure
    } finally {
      setBatchMetaLoading(false);
    }
  };

  // Function to load data with selected batches
  const handleLoadData = async () => {
    if (!panelId || selectedBatches.length === 0) {
      message.error('Please select at least one batch');
      return;
    }

    const dateTimeRange = form.getFieldValue('dateTimeRange');
    if (!dateTimeRange || !dateTimeRange[0] || !dateTimeRange[1]) {
      message.error('Date range is required');
      return;
    }

    setLoading(true);
    try {
      const [startDateTime, endDateTime] = dateTimeRange;

      const apiPayload = {
        start_datetime: startDateTime.format('YYYY-MM-DD HH:mm:ss'),
        end_datetime: endDateTime.format('YYYY-MM-DD HH:mm:ss'),
        batch_ids: selectedBatches,
        x_axis: 'DateTime', // Default X-axis
        y_axis: [], // Will be populated from API response
        identifier: 'BatchId', // Default identifier should be BatchId
        compare_limit: compareLimit
      };

      const response = await postRequest('/file/explore-plc/batch-comparison', apiPayload);

      if (response.data.data) {
        const apiData = response.data.data;



        // Get available columns from API response - now from columnOptions
        const columnOptions = apiData.columnOptions || {};
        const availableColumns = Object.keys(columnOptions);

        // Auto-select first column for Y-axis when data loads
        const firstColumn = availableColumns.length > 0 ? availableColumns[0] : null;

        // Update configuration with loaded data and auto-selected first column
        const loadedConfiguration: PLCPanelConfiguration = {
          dataRange: {
            startDate: startDateTime.format('YYYY-MM-DD'),
            endDate: endDateTime.format('YYYY-MM-DD'),
            startTime: startDateTime.format('HH:mm'),
            endTime: endDateTime.format('HH:mm'),
          },
          basic: {
            xAxisColumn: 'DateTime',
            selectedColumns: {
              indices: firstColumn ? [0] : [],
              headers: firstColumn ? [firstColumn] : []
            },
            group: 'BatchId', // Default to BatchId instead of None
          },
          advanced: {
            windowMode: false,
          },
          title: firstColumn ? `Time Series - ${firstColumn}` : 'Time Series - No Data Selected',
          panelType: 'PLCTimeSeriesPanel' as any,
          lastModified: new Date().toISOString(),
          apiData: {
            ...apiData,
            selectedBatches: selectedBatches,
            availableBatches: availableBatches,
            compareLimit: compareLimit,
            columns: availableColumns
          }
        };

        onConfigurationSave(panelId, loadedConfiguration);
        setIsDataLoaded(true);

        // Update form with loaded data and auto-selected first column
        form.setFieldsValue({
          xAxisColumn: 'DateTime',
          yAxis: firstColumn ? [firstColumn] : [],
          group: 'BatchId' // Default to BatchId
        });

        // Save configuration with auto-selected column to trigger chart rendering
        if (firstColumn) {
          onConfigurationSave(panelId, loadedConfiguration);
        }

        // Data loaded successfully - no need for message
      } else {
        message.error('Failed to load data');
      }
    } catch (error) {
      console.error('Error loading data:', error);
      message.error('Error loading data');
    } finally {
      setLoading(false);
    }
  };

  // Reusable function to call batch-comparison API with current configuration
  const callBatchComparisonAPI = async (xAxis: string, yAxis: string[], identifier: string) => {
    if (!isDataLoaded || !configuration?.apiData?.selectedBatches) {
      return null;
    }

    const dateTimeRange = form.getFieldValue('dateTimeRange');
    if (!dateTimeRange || !dateTimeRange[0] || !dateTimeRange[1]) {
      return null;
    }

    const [startDateTime, endDateTime] = dateTimeRange;

    const apiPayload = {
      start_datetime: startDateTime.format('YYYY-MM-DD HH:mm:ss'),
      end_datetime: endDateTime.format('YYYY-MM-DD HH:mm:ss'),
      batch_ids: configuration.apiData.selectedBatches,
      x_axis: xAxis,
      y_axis: yAxis,
      identifier: identifier, // Send the identifier as-is
      compare_limit: configuration.apiData.compareLimit || 5
    };

    try {
      const response = await postRequest('/file/explore-plc/batch-comparison', apiPayload);
      return response.data.data;
    } catch (error) {
      console.error('Error calling batch-comparison API:', error);
      message.error('Failed to update chart data');
      return null;
    }
  };

  // Handle Y-axis changes immediately (without submit)
  const handleYAxisChange = async (selectedYAxis: string[]) => {
    if (!isDataLoaded || !panelId) return;

    // If no Y-axis columns selected, don't make API call - show empty state
    if (!selectedYAxis || selectedYAxis.length === 0) {
      // Update configuration with empty selectedColumns to show no data state
      const updatedConfiguration: PLCPanelConfiguration = {
        ...configuration,
        basic: {
          xAxisColumn: configuration?.basic?.xAxisColumn || 'DateTime',
          selectedColumns: { indices: [], headers: [] },
          group: configuration?.basic?.group || 'BatchId',
        },
        lastModified: new Date().toISOString(),
      };
      onConfigurationSave(panelId, updatedConfiguration);
      return;
    }

    setYAxisLoading(true);
    try {
      const yAxisColumns = selectedYAxis;
      const currentXAxis = configuration?.basic?.xAxisColumn || 'DateTime';
      const currentIdentifier = configuration?.basic?.group || 'BatchId';

      // Make API call with new Y-axis
      const apiData = await callBatchComparisonAPI(currentXAxis, yAxisColumns, currentIdentifier);



      const selectedColumns = {
        indices: yAxisColumns.map((col: string) => availableColumns.indexOf(col)).filter((idx: number) => idx !== -1),
        headers: yAxisColumns
      };

      const updatedConfiguration: PLCPanelConfiguration = {
        ...configuration,
        basic: {
          xAxisColumn: currentXAxis,
          selectedColumns: selectedColumns,
          group: currentIdentifier,
        },
        title: `Time Series - ${yAxisColumns.join(', ')}`,
        lastModified: new Date().toISOString(),
        apiData: apiData ? { ...configuration?.apiData, ...apiData } : configuration?.apiData,
      };



      onConfigurationSave(panelId, updatedConfiguration);
    } catch (error) {
      console.error('Error updating Y-axis:', error);
      message.error('Failed to update Y-axis selection');
    } finally {
      setYAxisLoading(false);
    }
  };

  // Handle X-axis changes immediately (without submit)
  const handleXAxisChange = async (selectedXAxis: string) => {
    if (!isDataLoaded || !panelId) return;

    setXAxisLoading(true);
    try {
      const currentYAxis = configuration?.basic?.selectedColumns?.headers || [];
      const currentIdentifier = configuration?.basic?.group || 'BatchId';

      // Make API call with new X-axis
      const apiData = await callBatchComparisonAPI(selectedXAxis, currentYAxis, currentIdentifier);

      const updatedConfiguration: PLCPanelConfiguration = {
        ...configuration,
        basic: {
          xAxisColumn: selectedXAxis || 'DateTime',
          selectedColumns: configuration?.basic?.selectedColumns,
          group: currentIdentifier,
        },
        lastModified: new Date().toISOString(),
        apiData: apiData ? { ...configuration?.apiData, ...apiData } : configuration?.apiData,
      };

      onConfigurationSave(panelId, updatedConfiguration);
    } catch (error) {
      console.error('Error updating X-axis:', error);
      message.error('Failed to update X-axis selection');
    } finally {
      setXAxisLoading(false);
    }
  };

  // Handle grouping changes immediately (without submit)
  const handleGroupChange = async (selectedGroup: string) => {
    if (!isDataLoaded || !panelId) return;

    setGroupLoading(true);
    try {
      const currentXAxis = configuration?.basic?.xAxisColumn || 'DateTime';
      const currentYAxis = configuration?.basic?.selectedColumns?.headers || [];

      // Make API call with new identifier
      const apiData = await callBatchComparisonAPI(currentXAxis, currentYAxis, selectedGroup);

      const updatedConfiguration: PLCPanelConfiguration = {
        ...configuration,
        basic: {
          xAxisColumn: currentXAxis,
          selectedColumns: configuration?.basic?.selectedColumns,
          group: selectedGroup || 'BatchId',
        },
        lastModified: new Date().toISOString(),
        apiData: apiData ? { ...configuration?.apiData, ...apiData } : configuration?.apiData,
      };

      onConfigurationSave(panelId, updatedConfiguration);
    } catch (error) {
      console.error('Error updating group:', error);
      message.error('Failed to update grouping selection');
    } finally {
      setGroupLoading(false);
    }
  };

  // Handle drawer close
  const handleClose = () => {
    // Don't reset fields or state - let them persist based on configuration
    // Only reset form if there's no configuration to restore
    if (!configuration) {
      form.resetFields();
      setSelectedGroup('BatchId');
      setWindowMode(false);
      setSelectedTargets([]);
      setAvailableBatches([]);
      setSelectedBatches([]);
      setIsDataLoaded(false);
    }
    onClose();
  };

  // Initialize form with configuration data
  useEffect(() => {
    if (open && configuration) {
      // Set state variables to match configuration
      setSelectedGroup(configuration.basic?.group || 'BatchId');
      setWindowMode(configuration.advanced?.windowMode || false);

      // Restore batch-related state from configuration
      if (configuration.apiData) {
        setIsDataLoaded(true);
        setSelectedBatches(configuration.apiData.selectedBatches || []);
        setCompareLimit(configuration.apiData.compareLimit || 5);
        
        // Restore availableBatches from stored configuration
        setAvailableBatches(configuration.apiData.availableBatches || []);
      } else {
        setIsDataLoaded(false);
        setSelectedBatches([]);
        setAvailableBatches([]);
        setCompareLimit(5);
      }

      // Set window configuration targets if they exist
      if (configuration.advanced?.windowConfig?.target) {
        setSelectedTargets(configuration.advanced.windowConfig.target);
      }

      // Combine date and time for RangePicker
      const startDateTime = configuration.dataRange?.startDate && configuration.dataRange?.startTime
        ? dayjs(`${configuration.dataRange.startDate} ${configuration.dataRange.startTime}`)
        : null;
      const endDateTime = configuration.dataRange?.endDate && configuration.dataRange?.endTime
        ? dayjs(`${configuration.dataRange.endDate} ${configuration.dataRange.endTime}`)
        : null;

      form.setFieldsValue({
        dateTimeRange: startDateTime && endDateTime ? [startDateTime, endDateTime] : null,
        xAxisColumn: configuration.basic?.xAxisColumn || 'DateTime',
        yAxis: configuration.basic?.selectedColumns?.headers || [],
        group: configuration.basic?.group || 'BatchId',
        windowMode: configuration.advanced?.windowMode || false,
        target: configuration.advanced?.windowConfig?.target || [],
        pre: configuration.advanced?.windowConfig?.pre || undefined,
        post: configuration.advanced?.windowConfig?.post || undefined,
        selectedBatches: configuration.apiData?.selectedBatches || [],
      });
    } else if (open && !configuration) {
      // Only reset if opening with no configuration (new panel)
      setSelectedGroup('BatchId');
      setWindowMode(false);
      setSelectedTargets([]);
      setAvailableBatches([]);
      setSelectedBatches([]);
      setIsDataLoaded(false);
      setCompareLimit(5);

      // Set default date range (last 7 days)
      const defaultStartDateTime = dayjs().subtract(7, 'days').startOf('day');
      const defaultEndDateTime = dayjs().endOf('day');

      form.setFieldsValue({
        dateTimeRange: [defaultStartDateTime, defaultEndDateTime],
        xAxisColumn: 'DateTime',
        yAxis: [],
        group: 'BatchId',
        windowMode: false,
        selectedBatches: [],
      });
    }
  }, [open, configuration, form]);

  // Check for transferred data from batch exploration
  useEffect(() => {
    if (open && !configuration) {
      const transferredData = sessionStorage.getItem('plcTransferData');
      if (transferredData) {
        try {
          const plcData = JSON.parse(transferredData);
          console.log('Found transferred PLC data:', plcData);

          // Check if the data is recent (within last 5 minutes)
          const isRecent = Date.now() - plcData.transferTimestamp < 5 * 60 * 1000;

          if (isRecent) {
            // Convert the transferred datetime strings to dayjs objects
            const startDateTime = dayjs(plcData.startDateTime);
            const endDateTime = dayjs(plcData.endDateTime);

            // Update form with transferred data
            form.setFieldsValue({
              dateTimeRange: [startDateTime, endDateTime],
              xAxisColumn: 'DateTime',
              yAxis: [],
              group: 'BatchId',
              windowMode: false,
              selectedBatches: plcData.batchIds || [],
            });

            // Set state variables
            setSelectedBatches(plcData.batchIds || []);

            // Show message to user
            message.success(`Data transferred from Batch exploration. Time range: ${startDateTime.format('YYYY-MM-DD HH:mm')} to ${endDateTime.format('YYYY-MM-DD HH:mm')}`);

            // Automatically trigger data loading
            setTimeout(() => {
              handleLoadData();
            }, 500);

            // Clear the transferred data to prevent reuse
            sessionStorage.removeItem('plcTransferData');
          } else {
            // Data is too old, remove it
            sessionStorage.removeItem('plcTransferData');
          }
        } catch (error) {
          console.error('Error parsing transferred PLC data:', error);
          sessionStorage.removeItem('plcTransferData');
        }
      }
    }
  }, [open, configuration, form]);

  // Handle form submission
  const handleSubmit = async (values: any) => {
    if (!panelId) {
      return;
    }

    // Validate that date time range is provided
    if (!values.dateTimeRange || !values.dateTimeRange[0] || !values.dateTimeRange[1]) {
      message.error('Please select date and time range before submitting');
      return;
    }

    const [startDateTime, endDateTime] = values.dateTimeRange;

    // Validate that start is before end
    if (startDateTime.isAfter(endDateTime)) {
      message.error('Start date/time cannot be after end date/time');
      return;
    }

    setLoading(true);
    try {
      // Step 1: Fetch batch metadata first
      const success = await fetchBatchMetadata(startDateTime, endDateTime);

      if (!success) {
        // Keep drawer open on error, don't proceed
        setLoading(false);
        return;
      }

      // Don't show success message - just proceed to batch selection
      // Don't save configuration yet - wait for second API call
      // Keep drawer open for batch selection - don't close

      // Don't show success message - just proceed
    } catch (error: any) {
      console.error('Error saving configuration:', error);
      message.error(error.message || 'Failed to load PLC data');
    } finally {
      setLoading(false);
    }
  };

  // Get available columns from API data - no defaults, everything dynamic
  const availableColumns = configuration?.apiData?.columns
    ? configuration.apiData.columns.filter((col: string) => col !== 'DateTime')
    : [];

  // X-axis dropdown should include DateTime + all available columns
  const allColumns = configuration?.apiData?.columns
    ? ['DateTime', ...configuration.apiData.columns]
    : ['DateTime'];
  
  // Identifier dropdown should include BatchId and all available columns (removed None)
  const groupingColumns = ['BatchId', ...availableColumns];

  // Get distinct values for the selected grouping column - now using batch names from series data
  const getDistinctGroupValues = (groupColumn: string): string[] => {
    if (!isDataLoaded) return [];

    // For BatchId, return the actual batch names from the loaded series data
    if (groupColumn === 'BatchId') {
      // Extract batch names from columnOptions series data
      const apiData = configuration?.apiData;
      if (apiData?.columnOptions) {
        const allBatchNames = new Set<string>();
        
        // Go through all columns and collect series names (batch names)
        Object.values(apiData.columnOptions).forEach((columnData: any) => {
          if (columnData?.series && Array.isArray(columnData.series)) {
            columnData.series.forEach((series: any) => {
              if (series?.name) {
                allBatchNames.add(series.name);
              }
            });
          }
        });
        
        return Array.from(allBatchNames);
      }
      return availableBatches; // Fallback to available batches if no series data
    }

    // For any other column, extract distinct values from the series data
    if (availableColumns.includes(groupColumn)) {
      const apiData = configuration?.apiData;
      if (apiData?.columnOptions?.[groupColumn]?.series) {
        const distinctValues = new Set<string>();
        apiData.columnOptions[groupColumn].series.forEach((series: any) => {
          if (series?.data && Array.isArray(series.data)) {
            series.data.forEach((point: any) => {
              if (point[1] !== null && point[1] !== undefined) {
                distinctValues.add(String(point[1]));
              }
            });
          }
        });
        return Array.from(distinctValues).slice(0, 50); // Limit to first 50 distinct values
      }
    }

    return [];
  };

  // Calculate pre/post options based on selected targets
  const calculatePrePostOptions = (allValues: string[], targetValues: string[]): { preOptions: number[], postOptions: number[] } => {
    if (targetValues.length === 0) return { preOptions: [], postOptions: [] };

    // Find the range of target indices
    const targetIndices = targetValues.map(val => allValues.indexOf(val)).filter(idx => idx !== -1).sort((a, b) => a - b);
    if (targetIndices.length === 0) return { preOptions: [], postOptions: [] };

    const minTargetIndex = targetIndices[0];
    const maxTargetIndex = targetIndices[targetIndices.length - 1];

    // Calculate pre options (batches before the first target)
    const preCount = minTargetIndex;
    const preOptions = preCount > 0 ? Array.from({ length: preCount }, (_, i) => i + 1) : [];

    // Calculate post options (batches after the last target)
    const postCount = allValues.length - 1 - maxTargetIndex;
    const postOptions = postCount > 0 ? Array.from({ length: postCount }, (_, i) => i + 1) : [];

    return { preOptions, postOptions };
  };

  // Handle target selection change
  const handleTargetChange = (values: string[]) => {
    setSelectedTargets(values);
    form.setFieldValue('target', values);

    // Clear pre/post selections when targets change
    form.setFieldValue('pre', undefined);
    form.setFieldValue('post', undefined);
  };

  // Handle window mode submit
  const handleWindowSubmit = async () => {
    if (!panelId || !isDataLoaded) return;

    const formValues = form.getFieldsValue();
    const { target, pre, post } = formValues;

    if (!target || target.length === 0) {
      message.error('Please select at least one target value');
      return;
    }

    const dateTimeRange = form.getFieldValue('dateTimeRange');
    if (!dateTimeRange || !dateTimeRange[0] || !dateTimeRange[1]) {
      message.error('Date range is required');
      return;
    }

    setWindowLoading(true);
    try {
      const [startDateTime, endDateTime] = dateTimeRange;
      const currentXAxis = configuration?.basic?.xAxisColumn || 'DateTime';
      const currentYAxis = configuration?.basic?.selectedColumns?.headers || [];
      const currentIdentifier = configuration?.basic?.group || 'None';

      // Prepare API payload with window configuration
      const apiPayload = {
        start_datetime: startDateTime.format('YYYY-MM-DD HH:mm'),
        end_datetime: endDateTime.format('YYYY-MM-DD HH:mm'),
        batch_ids: configuration?.apiData?.selectedBatches || [],
        x_axis: currentXAxis,
        y_axis: currentYAxis,
        identifier: currentIdentifier,
        compare_limit: configuration?.apiData?.compareLimit || 5,
        window_mode: true,
        window_config: {
          target_values: target,
          pre: pre || 0,
          post: post || 0
        }
      };

      // Make API call with window configuration
      const response = await postRequest('/file/explore-plc/batch-comparison', apiPayload);

      if (response.data.data) {
        const apiData = response.data.data;

        // Create window mode configuration
        const windowConfig = {
          enabled: true,
          groupColumn: selectedGroup,
          target: target,
          pre: pre || 0,
          post: post || 0
        };

        const updatedConfiguration: PLCPanelConfiguration = {
          ...configuration,
          advanced: {
            ...configuration?.advanced,
            windowMode: true,
            windowConfig: windowConfig,
          },
          lastModified: new Date().toISOString(),
          // Update apiData with new windowed data
          apiData: {
            ...configuration?.apiData,
            ...apiData,
            windowMode: true,
            windowConfig: windowConfig
          }
        };

        onConfigurationSave(panelId, updatedConfiguration);
        message.success('Window configuration applied successfully');
      } else {
        message.error('Failed to apply window configuration');
      }
    } catch (error) {
      console.error('Error applying window configuration:', error);
      message.error('Failed to apply window configuration');
    } finally {
      setWindowLoading(false);
    }
  };

  // Handle window mode toggle
  const handleWindowModeChange = (e: any) => {
    const checked = e.target.checked;
    setWindowMode(checked);
    form.setFieldValue('windowMode', checked);
  };

  // Handle group selection change
  const handleGroupSelectionChange = async (value: string) => {
    setSelectedGroup(value);
    form.setFieldValue('group', value);

    // Call the handleGroupChange to save configuration and make API call
    handleGroupChange(value);
  };

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '16px',
            fontWeight: 600
          }}>
            <SettingOutlined style={{ color: '#1890ff' }} />
            Panel Configuration
          </div>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={handleClose}
            size="small"
          />
        </div>
      }
      placement="right"
      width={450}
      open={open}
      onClose={handleClose}
      closable={false}
      styles={{
        body: { padding: '0' }
      }}
    >
      <div className="plc-config-drawer">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="plc-config-form"
        >
          {/* Data Selection Section */}
          <div style={{
            background: '#ffffff',
            borderRadius: '12px',
            padding: '20px',
            margin: '16px',
            marginBottom: '20px',
            border: '1px solid #e8e8e8',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
          }}>
            {/* Date and Time Range Selection */}
            <Form.Item
              label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Date & Time Range</span>}
              name="dateTimeRange"
              style={{ marginBottom: '16px' }}
              rules={[
                { required: true, message: 'Please select date and time range!' }
              ]}
            >
              <RangePicker
                showTime={{ format: 'HH:mm' }}
                format="YYYY-MM-DD HH:mm"
                placeholder={['Start Date & Time', 'End Date & Time']}
                style={{ width: '100%', borderRadius: '8px' }}
                size="middle"
              />
            </Form.Item>

            {/* Submit Button in Data Selection Zone */}
            <div style={{ marginTop: '20px' }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={batchMetaLoading}
                style={{
                  width: '100%',
                  height: '40px',
                  fontSize: '14px',
                  fontWeight: 600,
                  borderRadius: '8px',
                  background: '#1890ff',
                  border: 'none'
                }}
                size="large"
              >
                {batchMetaLoading ? 'Fetching Batches...' : 'Submit Date Range'}
              </Button>
            </div>

            {/* Batch Selection Section */}
            {availableBatches.length > 0 && (
              <div style={{
                background: '#f0f8ff',
                borderRadius: '8px',
                padding: '16px',
                border: '1px solid #91d5ff',
                marginTop: '16px'
              }}>
                <div style={{
                  marginBottom: '12px',
                  fontSize: '13px',
                  fontWeight: 500,
                  color: '#1890ff'
                }}>
                  📊 Batch Selection (Max: {compareLimit})
                </div>

                <Form.Item
                  label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Select Batches</span>}
                  name="selectedBatches"
                  style={{ marginBottom: '12px' }}
                  rules={[
                    { required: true, message: 'Please select at least one batch!' },
                    {
                      validator: (_, value) => {
                        if (value && value.length > compareLimit) {
                          return Promise.reject(new Error(`Maximum ${compareLimit} batches allowed`));
                        }
                        return Promise.resolve();
                      }
                    }
                  ]}
                >
                  <Select
                    mode="multiple"
                    placeholder={`Select up to ${compareLimit} batches`}
                    value={selectedBatches}
                    onChange={setSelectedBatches}
                    size="middle"
                    style={{
                      borderRadius: '8px',
                      width: '100%'
                    }}
                    maxTagCount="responsive"
                  >
                    {availableBatches.map(batch => (
                      <Option key={batch} value={batch}>
                        {batch}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Button
                  type="primary"
                  loading={loading && !batchMetaLoading}
                  disabled={selectedBatches.length === 0}
                  size="middle"
                  style={{
                    borderRadius: '8px',
                    fontWeight: 500,
                    height: '36px',
                    background: '#52c41a',
                    borderColor: '#52c41a',
                    boxShadow: '0 2px 4px rgba(82, 196, 26, 0.2)'
                  }}
                  block
                  onClick={handleLoadData}
                >
                  Load Data ({selectedBatches.length} batches selected)
                </Button>

                {isDataLoaded && (
                  <div style={{
                    textAlign: 'center',
                    marginTop: '8px',
                    fontSize: '12px',
                    color: '#52c41a',
                    fontWeight: 500
                  }}>
                    ✅ Data loaded - Configure axes below
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Basic Settings */}
          <div style={{
            background: '#ffffff',
            borderRadius: '12px',
            padding: '20px',
            margin: '0 16px 20px 16px',
            border: `1px solid ${isDataLoaded ? '#e8e8e8' : '#d9d9d9'}`,
            boxShadow: isDataLoaded ? '0 2px 8px rgba(0,0,0,0.06)' : 'none',
            opacity: isDataLoaded ? 1 : 0.5
          }}>
            <div style={{
              marginBottom: '20px',
              paddingBottom: '12px',
              borderBottom: `2px solid ${isDataLoaded ? '#1890ff' : '#d9d9d9'}`
            }}>
              <Title level={5} style={{
                margin: 0,
                color: isDataLoaded ? '#1890ff' : '#999',
                fontSize: '16px',
                fontWeight: 600,
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                ⚙️ Basic Settings
                {!isDataLoaded && <span style={{ fontSize: '12px', color: '#999', fontWeight: 400 }}>(Load data first)</span>}
              </Title>
            </div>
            <div>
              <Form.Item
                label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>X Axis</span>}
                name="xAxisColumn"
                style={{ marginBottom: '16px' }}
              >
                <Select
                  placeholder="Select X Axis Column"
                  disabled={!isDataLoaded}
                  loading={xAxisLoading}
                  onChange={handleXAxisChange}
                  size="middle"
                  style={{
                    borderRadius: '8px',
                    width: '100%'
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase()?.includes(input.toLowerCase())
                  }
                >
                  {allColumns.map(column => (
                    <Option key={column} value={column}>
                      {column}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Y Axis (Multi-select)</span>}
                name="yAxis"
                style={{ marginBottom: '16px' }}
              >
                <Select
                  mode="multiple"
                  placeholder="Select columns for Y Axis"
                  allowClear
                  disabled={!isDataLoaded}
                  loading={yAxisLoading}
                  onChange={handleYAxisChange}
                  size="middle"
                  style={{
                    borderRadius: '8px',
                    width: '100%'
                  }}
                  maxTagCount="responsive"
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase()?.includes(input.toLowerCase())
                  }
                >
                  {availableColumns.map(column => (
                    <Option key={column} value={column}>
                      {column}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Identifier</span>}
                name="group"
                style={{ marginBottom: '8px' }}
              >
                <Select
                  placeholder="BatchId"
                  disabled={!isDataLoaded}
                  loading={groupLoading}
                  onChange={handleGroupSelectionChange}
                  size="middle"
                  style={{
                    borderRadius: '8px',
                    width: '100%'
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase()?.includes(input.toLowerCase())
                  }
                >
                  {groupingColumns.map(column => (
                    <Option key={column} value={column}>
                      {column}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </div>

          {/* Advanced Settings */}
          <div style={{
            background: '#ffffff',
            borderRadius: '12px',
            padding: '20px',
            margin: '0 16px 20px 16px',
            border: `1px solid ${isDataLoaded ? '#e8e8e8' : '#d9d9d9'}`,
            boxShadow: isDataLoaded ? '0 2px 8px rgba(0,0,0,0.06)' : 'none',
            opacity: isDataLoaded ? 1 : 0.5
          }}>
            <div style={{
              marginBottom: '20px',
              paddingBottom: '12px',
              borderBottom: `2px solid ${isDataLoaded ? '#fa8c16' : '#d9d9d9'}`
            }}>
              <Title level={5} style={{
                margin: 0,
                color: isDataLoaded ? '#fa8c16' : '#999',
                fontSize: '16px',
                fontWeight: 600,
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                🔧 Advanced Settings
                {!isDataLoaded && <span style={{ fontSize: '12px', color: '#999', fontWeight: 400 }}>(Load data first)</span>}
              </Title>
            </div>
            <div>
              <Form.Item
                name="windowMode"
                valuePropName="checked"
                style={{ marginBottom: '16px' }}
              >
                <Checkbox
                  disabled={!isDataLoaded}
                  onChange={handleWindowModeChange}
                  style={{
                    fontSize: '13px',
                    color: isDataLoaded ? '#666' : '#999'
                  }}
                >
                  Window Mode
                </Checkbox>
              </Form.Item>

              {/* Window Mode Configuration */}
              {windowMode && (
                <div style={{
                  background: '#f6ffed',
                  borderRadius: '8px',
                  padding: '16px',
                  border: '1px solid #b7eb8f',
                  marginBottom: '16px'
                }}>
                  <div style={{
                    marginBottom: '12px',
                    fontSize: '13px',
                    fontWeight: 600,
                    color: '#52c41a'
                  }}>
                    🎯 Window Configuration
                  </div>

                  <Form.Item
                    label={<span style={{ fontSize: '12px', fontWeight: 500, color: '#666' }}>Target (Multi-select)</span>}
                    name="target"
                    style={{ marginBottom: '12px' }}
                  >
                    <Select
                      mode="multiple"
                      placeholder={`Select ${selectedGroup} values`}
                      allowClear
                      size="small"
                      style={{ borderRadius: '6px', width: '100%' }}
                      maxTagCount="responsive"
                      onChange={handleTargetChange}
                    >
                      {getDistinctGroupValues(selectedGroup).map(value => (
                        <Option key={value} value={value}>
                          {value}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <div style={{ display: 'flex', gap: '8px' }}>
                    <Form.Item
                      label={<span style={{ fontSize: '12px', fontWeight: 500, color: '#666' }}>Pre</span>}
                      name="pre"
                      style={{ flex: 1, marginBottom: '8px' }}
                    >
                      <Select
                        placeholder="Select"
                        size="small"
                        style={{ borderRadius: '6px' }}
                        disabled={selectedTargets.length === 0}
                      >
                        {calculatePrePostOptions(getDistinctGroupValues(selectedGroup), selectedTargets).preOptions.map(num => (
                          <Option key={num} value={num}>
                            {num} ({num === 1 ? 'immediate previous' : `previous ${num} combined`})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>

                    <Form.Item
                      label={<span style={{ fontSize: '12px', fontWeight: 500, color: '#666' }}>Post</span>}
                      name="post"
                      style={{ flex: 1, marginBottom: '8px' }}
                    >
                      <Select
                        placeholder="Select"
                        size="small"
                        style={{ borderRadius: '6px' }}
                        disabled={selectedTargets.length === 0}
                      >
                        {calculatePrePostOptions(getDistinctGroupValues(selectedGroup), selectedTargets).postOptions.map(num => (
                          <Option key={num} value={num}>
                            {num} ({num === 1 ? 'immediate next' : `next ${num} combined`})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </div>

                  {/* Window Mode Submit Button */}
                  <Button
                    type="primary"
                    onClick={handleWindowSubmit}
                    loading={windowLoading}
                    style={{
                      width: '100%',
                      height: '32px',
                      fontSize: '12px',
                      fontWeight: 600,
                      borderRadius: '6px',
                      background: '#52c41a',
                      border: 'none',
                      marginTop: '8px'
                    }}
                    size="small"
                  >
                    Apply Window Settings
                  </Button>
                </div>
              )}
            </div>
          </div>
        </Form>
      </div>
    </Drawer>
  );
};

export default PLCPanelConfigDrawer;
