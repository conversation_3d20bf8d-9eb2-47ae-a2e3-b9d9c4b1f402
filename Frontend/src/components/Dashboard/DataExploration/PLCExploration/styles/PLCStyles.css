/* PLC Exploration Specific Styles */

/* PLC Grid Container */
.plc-grid-container {
  position: relative;
}

/* PLC Grid Layout */
.plc-layout {
  position: relative;
  min-height: 400px;
}

.plc-grid-item-wrapper {
  transition: all 0.3s ease;
}

/* PLC Grid Item */
.plc-grid-item {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  overflow: hidden;
}

.plc-grid-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  border-color: #1890ff;
}

/* PLC Grid Item Header */
.plc-grid-item-header {
  background: linear-gradient(90deg, #f0f2f5 0%, #fafafa 100%);
  border-bottom: 1px solid #e8e8e8;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 48px;
}

.plc-drag-handle {
  cursor: move;
  flex: 1;
  user-select: none;
}

.plc-drag-handle:hover {
  color: #1890ff;
}

.plc-grid-item-controls {
  display: flex;
  gap: 4px;
  align-items: center;
}

.plc-grid-item-controls .ant-btn {
  border: none;
  box-shadow: none;
  padding: 4px 8px;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plc-grid-item-controls .ant-btn:hover {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

/* PLC Grid Item Content */
.plc-grid-item-content {
  padding: 16px;
  height: calc(100% - 48px);
  overflow: auto;
}

/* PLC Sidebar Styles */
.plc-sidebar-collapse .ant-collapse-header {
  padding: 12px 16px !important;
  background: #f0f2f5 !important;
}

.plc-sidebar-collapse .ant-collapse-content-box {
  padding: 16px !important;
}

/* Removed unused .plc-panel-item styles - using component-item instead */

/* Drop Indicator - matches batch exploration exactly */
.drop-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 2px dashed #ccc;
  border-radius: 8px;
  text-align: center;
  color: #666;
  font-size: 16px;
  z-index: 10;
}

/* PLC Configuration Drawer */
.plc-config-drawer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.config-section-container {
  border-bottom: 1px solid #e8e8e8;
  background: white;
}

.config-section-header {
  background: #f5f5f5;
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
}

.config-section-header .section-title {
  margin: 0 !important;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.config-section-content {
  padding: 20px 24px;
}

.date-time-row {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  margin-bottom: 16px;
}

.arrow-separator {
  color: #8c8c8c;
  font-size: 14px;
  margin-bottom: 24px;
  font-weight: 500;
}

.submit-section {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.plc-config-form .ant-form-item {
  margin-bottom: 16px;
}

.plc-config-form .ant-form-item-label > label {
  font-weight: 500;
  color: #595959;
  font-size: 12px;
}

.plc-config-form .ant-select,
.plc-config-form .ant-picker {
  border-radius: 4px;
}

.plc-config-form .ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
  border-radius: 4px;
  font-weight: 500;
}

/* PLC Time Series Panel Empty State */
.plc-timeseries-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 40px 20px;
}

.plc-timeseries-empty .anticon {
  margin-bottom: 16px;
  opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .plc-grid-item-header {
    padding: 8px 12px;
    min-height: 40px;
  }
  
  .plc-grid-item-content {
    padding: 12px;
    height: calc(100% - 40px);
  }
  
  .plc-grid-item-controls .ant-btn {
    padding: 2px 6px;
  }
}

/* Fullscreen Mode Adjustments */
.plc-fullscreen-content {
  height: 100vh;
  width: 100vw;
  background: white;
}

.plc-fullscreen-content .plc-grid-item-content {
  height: calc(100vh - 60px);
  padding: 20px;
}

/* Loading States */
.plc-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Optimized for performance - minimal animations */

/* Custom scrollbar for PLC components */
.plc-grid-item-content::-webkit-scrollbar {
  width: 6px;
}

.plc-grid-item-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.plc-grid-item-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.plc-grid-item-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
