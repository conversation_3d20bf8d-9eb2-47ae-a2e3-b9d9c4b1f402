import React, { useState, useCallback, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import { 
  PLCComponentType, 
  PLCGridItemData, 
  PLCLayout, 
  PLCLayoutItem, 
  PLCGridLayoutProps,
  PLC_GRID_CONFIG,
  PLC_PANEL_SIZES
} from './types/PLCTypes';
import PLCGridItem from './PLCGridItem';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ReactGridLayout = WidthProvider(RGL);

const PLCGridLayout = forwardRef<{getLayout: () => PLCLayout, getItems: () => PLCGridItemData[]}, PLCGridLayoutProps>(({
  items,
  layout,
  onLayoutChange,
  onItemsChange,
  onDrop,
  onPanelRemove,
  onOpenConfiguration
}, ref) => {
  const [nextId, setNextId] = useState(1);
  const [dragIndicator, setDragIndicator] = useState<{x: number, y: number, show: boolean}>({
    x: 0,
    y: 0,
    show: false
  });
  const gridContainerRef = useRef<HTMLDivElement>(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    getLayout: () => layout,
    getItems: () => items
  }));

  // Handle drop from sidebar
  const handleContainerDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragIndicator(prev => ({ ...prev, show: false }));

    const componentType = e.dataTransfer.getData('component') as PLCComponentType;
    if (!componentType || !gridContainerRef.current) return;

    const existingPanel = items.find(item => item.type === componentType);
    if (existingPanel) {
      return;
    }

    const rect = gridContainerRef.current.getBoundingClientRect();
    const x = Math.floor((e.clientX - rect.left) / 100); // Approximate column width
    const y = Math.floor((e.clientY - rect.top) / PLC_GRID_CONFIG.ROW_HEIGHT);

    onDrop(componentType, x, y);
  }, [items, onDrop]);

  // Handle drag over for visual feedback
  const handleContainerDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';

    if (!gridContainerRef.current) return;

    const rect = gridContainerRef.current.getBoundingClientRect();
    const x = Math.floor((e.clientX - rect.left) / 100);
    const y = Math.floor((e.clientY - rect.top) / PLC_GRID_CONFIG.ROW_HEIGHT);

    setDragIndicator({
      x: Math.max(0, Math.min(x, PLC_GRID_CONFIG.COLS - PLC_PANEL_SIZES.DEFAULT.w)),
      y: Math.max(0, y),
      show: true
    });
  }, []);

  // Handle drag leave
  const handleContainerDragLeave = useCallback((e: React.DragEvent) => {
    // Only hide indicator if leaving the container entirely
    if (!gridContainerRef.current?.contains(e.relatedTarget as Node)) {
      setDragIndicator(prev => ({ ...prev, show: false }));
    }
  }, []);

  // Handle layout change from react-grid-layout
  const handleLayoutChange = useCallback((newLayout: any[]) => {
    const plcLayout: PLCLayout = newLayout.map(item => ({
      i: item.i,
      x: item.x,
      y: item.y,
      w: item.w,
      h: item.h,
      minW: item.minW,
      minH: item.minH,
      maxW: item.maxW,
      maxH: item.maxH,
      static: item.static,
      isDraggable: item.isDraggable,
      isResizable: item.isResizable,
    }));
    onLayoutChange(plcLayout);
  }, [onLayoutChange]);

  // Handle drop on grid (from react-grid-layout)
  const handleDrop = useCallback((layout: any[], layoutItem: any, _event: Event) => {
    // Grid drop handled by container drop
  }, []);



  // Handle panel removal
  const handlePanelRemove = useCallback((panelId: string) => {
    const updatedItems = items.filter(item => item.id !== panelId);
    const updatedLayout = layout.filter(item => item.i !== panelId);
    
    onItemsChange(updatedItems);
    onLayoutChange(updatedLayout);
    onPanelRemove(panelId);
  }, [items, layout, onItemsChange, onLayoutChange, onPanelRemove]);

  // Render drop indicator for empty state
  const renderDropIndicator = () => {
    if (!dragIndicator.show) return null;

    return (
      <div
        className="drop-indicator"
        style={{
          position: 'absolute',
          left: `${(dragIndicator.x / PLC_GRID_CONFIG.COLS) * 100}%`,
          top: `${dragIndicator.y * PLC_GRID_CONFIG.ROW_HEIGHT}px`,
          width: `${(PLC_PANEL_SIZES.DEFAULT.w / PLC_GRID_CONFIG.COLS) * 100}%`,
          height: `${PLC_PANEL_SIZES.DEFAULT.h * PLC_GRID_CONFIG.ROW_HEIGHT}px`,
          backgroundColor: 'rgba(24, 144, 255, 0.2)',
          border: '2px dashed #1890ff',
          borderRadius: '4px',
          pointerEvents: 'none',
          zIndex: 1000,
        }}
      />
    );
  };

  // Empty state when no panels (exactly like batch exploration)
  if (items.length === 0) {
    return (
      <div
        className="plc-grid-container"
        ref={gridContainerRef}
        onDragOver={handleContainerDragOver}
        onDrop={handleContainerDrop}
        onDragLeave={handleContainerDragLeave}
        style={{ height: '100%', position: 'relative' }}
      >
        <div className="drop-indicator">
          Drag components from the sidebar and drop here
        </div>
      </div>
    );
  }

  return (
    <div
      className="plc-grid-container"
      ref={gridContainerRef}
      onDragOver={handleContainerDragOver}
      onDrop={handleContainerDrop}
      onDragLeave={handleContainerDragLeave}
      style={{ height: '100%', position: 'relative' }}
    >
      {renderDropIndicator()}
      <ReactGridLayout
        className="plc-layout"
        layout={layout}
        cols={PLC_GRID_CONFIG.COLS}
        rowHeight={PLC_GRID_CONFIG.ROW_HEIGHT}
        margin={PLC_GRID_CONFIG.MARGIN}
        containerPadding={PLC_GRID_CONFIG.CONTAINER_PADDING}
        onLayoutChange={handleLayoutChange}
        onDrop={handleDrop}
        isDroppable={true}
        droppingItem={{ 
          i: '__dropping-elem__', 
          w: PLC_PANEL_SIZES.DEFAULT.w, 
          h: PLC_PANEL_SIZES.DEFAULT.h 
        }}
        preventCollision={false}
        compactType="vertical"
        useCSSTransforms={true}
        allowOverlap={false}
        draggableHandle=".plc-drag-handle"
        resizeHandles={['s', 'e', 'n', 'w', 'se', 'sw', 'ne', 'nw']}
      >
        {items.map(item => {
          const itemLayout = layout.find(l => l.i === item.id);
          return (
            <div key={item.id} className="plc-grid-item-wrapper">
              <PLCGridItem
                data={item}
                layout={itemLayout}
                onRemove={handlePanelRemove}
                onOpenConfiguration={onOpenConfiguration}
              />
            </div>
          );
        })}
      </ReactGridLayout>
    </div>
  );
});

PLCGridLayout.displayName = 'PLCGridLayout';

export default PLCGridLayout;
